/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
}

[data-theme="dark"] .navbar {
    background-color: rgba(15, 23, 42, 0.95);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo a {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--text-secondary);
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.theme-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.theme-toggle:hover {
    color: var(--primary-color);
    background-color: var(--bg-secondary);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    transition: all var(--transition-fast);
    border-radius: var(--radius-sm);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    padding-top: 70px;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-6xl);
    font-weight: 800;
    margin-bottom: var(--spacing-md);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--font-size-2xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.hero-social {
    display: flex;
    gap: var(--spacing-lg);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-full);
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-avatar {
    position: relative;
}

.avatar-placeholder {
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 120px;
    color: var(--text-inverse);
    box-shadow: var(--shadow-xl);
}

.hero-scroll {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
}

.scroll-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-full);
    color: var(--primary-color);
    animation: pulse 2s infinite;
}



/* Timeline */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--border-color);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-2xl);
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row-reverse;
}

.timeline-content {
    background-color: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    width: calc(50% - var(--spacing-xl));
    position: relative;
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-right: var(--spacing-xl);
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: var(--spacing-xl);
}

.timeline-dot {
    position: absolute;
    left: 50%;
    width: 16px;
    height: 16px;
    background-color: var(--primary-color);
    border-radius: var(--radius-full);
    transform: translateX(-50%);
    z-index: 1;
}

.timeline-date {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.timeline-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.timeline-company {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.timeline-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Education Grid */
.education-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
    max-width: 1000px;
    margin: 0 auto;
}

.education-card {
    background-color: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
    display: flex;
    gap: var(--spacing-lg);
    align-items: flex-start;
}

.education-card:hover {
    transform: translateY(-5px);
}

.education-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.education-content {
    flex: 1;
}

.education-degree {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    line-height: 1.3;
}

.education-institution {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.education-location {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.education-period {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-md);
    font-style: italic;
}

.education-details {
    list-style: none;
    margin: 0;
    padding: 0;
}

.education-details li {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    position: relative;
    padding-left: var(--spacing-md);
}

.education-details li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* Publications */
.publications-list {
    max-width: 900px;
    margin: 0 auto;
}

.publication-item {
    background-color: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
    transition: transform var(--transition-normal);
}

.publication-item:hover {
    transform: translateY(-2px);
}

.publication-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.publication-authors {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.publication-venue {
    color: var(--primary-color);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

/* Blog Grid */
.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.blog-card {
    background-color: var(--bg-card);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.blog-image {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-3xl);
}

.blog-content {
    padding: var(--spacing-xl);
}

.blog-date {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-sm);
}

.blog-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.blog-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.blog-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.blog-tag {
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
}

.blog-cta {
    text-align: center;
}



/* Footer */
.footer {
    background-color: var(--bg-tertiary);
    padding: var(--spacing-2xl) 0;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-social {
    display: flex;
    gap: var(--spacing-md);
}

.footer-social .social-link {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-base);
}
