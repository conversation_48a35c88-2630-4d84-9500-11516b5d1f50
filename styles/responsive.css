/* Responsive Design */

/* Large screens (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }
    
    .hero-title {
        font-size: var(--font-size-6xl);
    }
    
    .section-title {
        font-size: var(--font-size-5xl);
    }
}

/* Medium screens (768px to 1199px) */
@media (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .hero-content {
        gap: var(--spacing-2xl);
    }
    
    .avatar-placeholder {
        width: 250px;
        height: 250px;
        font-size: 100px;
    }
    
    .about-highlights {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .education-grid {
        grid-template-columns: 1fr;
    }
}

/* Small screens (768px to 991px) */
@media (max-width: 991px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: var(--bg-primary);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: var(--spacing-3xl);
        transition: left var(--transition-normal);
        border-top: 1px solid var(--border-color);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-2xl);
    }
    
    .hero-title {
        font-size: var(--font-size-5xl);
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .timeline::before {
        left: 20px;
    }
    
    .timeline-item {
        flex-direction: row !important;
        padding-left: 50px;
    }
    
    .timeline-content {
        width: 100%;
        margin: 0 !important;
    }
    
    .timeline-dot {
        left: 20px;
        transform: translateX(-50%);
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .footer-content {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
    }
}

/* Extra small screens (up to 767px) */
@media (max-width: 767px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .nav-container {
        padding: 0 var(--spacing-md);
    }
    
    .hero {
        padding-top: 90px;
        min-height: auto;
        padding-bottom: var(--spacing-3xl);
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-xl);
    }
    
    .hero-description {
        font-size: var(--font-size-base);
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .avatar-placeholder {
        width: 200px;
        height: 200px;
        font-size: 80px;
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
    
    .about-highlights {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .highlight-item {
        padding: var(--spacing-lg);
    }
    
    .education-grid {
        grid-template-columns: 1fr;
    }

    .education-card {
        flex-direction: column;
        text-align: center;
    }

    .education-icon {
        align-self: center;
    }
    
    .blog-grid {
        grid-template-columns: 1fr;
    }
    
    .timeline-item {
        padding-left: 40px;
    }
    
    .timeline::before {
        left: 15px;
    }
    
    .timeline-dot {
        left: 15px;
    }
    
    .timeline-content {
        padding: var(--spacing-lg);
    }
    
    .contact-form {
        padding: var(--spacing-lg);
    }
    
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .hero-social {
        justify-content: center;
    }
    
    .social-link {
        width: 45px;
        height: 45px;
    }
}

/* Very small screens (up to 480px) */
@media (max-width: 480px) {
    :root {
        --spacing-xs: 0.125rem;
        --spacing-sm: 0.375rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1rem;
        --spacing-xl: 1.5rem;
        --spacing-2xl: 2rem;
        --spacing-3xl: 2.5rem;
    }
    
    .nav-container {
        height: 60px;
    }
    
    .nav-menu {
        top: 60px;
        height: calc(100vh - 60px);
    }
    
    .hero {
        padding-top: 80px;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .avatar-placeholder {
        width: 150px;
        height: 150px;
        font-size: 60px;
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .card {
        padding: var(--spacing-lg);
    }
    
    .timeline-content {
        padding: var(--spacing-md);
    }
    
    .timeline-title {
        font-size: var(--font-size-lg);
    }
    
    .contact-form {
        padding: var(--spacing-md);
    }
    
    .form-group input,
    .form-group textarea {
        padding: var(--spacing-sm);
    }
    
    .blog-content {
        padding: var(--spacing-lg);
    }
    
    .blog-image {
        height: 150px;
        font-size: var(--font-size-2xl);
    }
}

/* Print styles */
@media print {
    .navbar,
    .hero-scroll,
    .contact-form,
    .theme-toggle {
        display: none !important;
    }
    
    .hero {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }
    
    section {
        padding: var(--spacing-lg) 0;
        break-inside: avoid;
    }
    
    .card,
    .timeline-item,
    .publication-item {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
    
    a {
        color: var(--text-primary) !important;
        text-decoration: underline;
    }
    
    .btn {
        border: 1px solid var(--border-color);
        background: none !important;
        color: var(--text-primary) !important;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .animate-fadeInUp,
    .animate-fadeInLeft,
    .animate-fadeInRight,
    .animate-pulse {
        animation: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
        --bg-secondary: #ffffff;
    }
    
    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
        --bg-secondary: #000000;
    }
    
    .card,
    .timeline-content,
    .contact-form {
        border: 2px solid var(--border-color);
    }
    
    .btn {
        border: 2px solid currentColor;
    }
}

/* Focus styles for accessibility */
@media (prefers-reduced-motion: no-preference) {
    *:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
    
    .btn:focus,
    .nav-link:focus,
    .social-link:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
}

/* Dark mode specific responsive adjustments */
[data-theme="dark"] {
    @media (max-width: 991px) {
        .nav-menu {
            background-color: var(--bg-primary);
            border-top-color: var(--border-color);
        }
    }
}

/* Landscape orientation on mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .hero {
        min-height: auto;
        padding: var(--spacing-xl) 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-xl);
    }
    
    .avatar-placeholder {
        width: 120px;
        height: 120px;
        font-size: 48px;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-buttons {
        flex-direction: row;
        gap: var(--spacing-md);
    }
    
    .btn {
        width: auto;
    }
}
